<?php

namespace App\Modules\Facebook;

use JsonException;
use Guzzle<PERSON>ttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Config;
use Psr\Http\Message\ResponseInterface;
use <PERSON><PERSON>\Socialite\Facades\Socialite;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use App\Modules\Facebook\Exceptions\FacebookTokenInvalid;

class FacebookClient
{
    private const FACEBOOK_GRAPH_VERSION = 'v20.0';
    private const FACEBOOK_APP_ID = '449082488087608';
    private const FACEBOOK_APP_SECRET = '********************************';
    private const FACEBOOK_GRAPH_API = 'https://graph.facebook.com';

    private array $permissions = [
        'ads_management',
        'pages_show_list',
        'leads_retrieval',
        'pages_manage_metadata',
        'pages_read_engagement',
        'pages_manage_ads',
        'business_management',
        'read_insights',
    ];

    private const APP = 'facebook';

    public function __construct(
        private readonly Client $client
    ) {
    }

    public function login(): RedirectResponse
    {
        // Setting facebook redirect url
        $redirectUrl = 'https://' . request()->getHost(). '/v1/facebook/callback';
        Config::set('services.facebook.redirect', $redirectUrl);

        return Socialite::driver(self::APP)
            ->scopes($this->permissions)
            ->redirect();
    }

    public function user()
    {
        $redirectUrl = 'https://' . request()->getHost(). '/v1/facebook/callback';
        Config::set('services.facebook.redirect', $redirectUrl);

        return Socialite::driver(self::APP)
            ->stateless()
            ->user();
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function listPages(string $accessToken): array
    {
        $url = sprintf('%s/%s/me/accounts', self::FACEBOOK_GRAPH_API, self::FACEBOOK_GRAPH_VERSION);

        $response = $this->client->get($url, [
            'query' => [
                'access_token' => $accessToken,
            ],
        ]);

        return $this->extractJsonResponseBody($response);
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function listAds(string $accessToken, string $pageId, ?string $after = null): array
    {
        $url = sprintf('%s/%s/%s/leadgen_forms', self::FACEBOOK_GRAPH_API, self::FACEBOOK_GRAPH_VERSION, $pageId);

        $queryParams = [
            'access_token' => $accessToken,
            'limit' => 50,
        ];

        if ($after !== null) {
            $queryParams['after'] = $after;
        }

        $response = $this->client->get($url, [
            'query' => $queryParams,
        ]);

        $responseData = $this->extractJsonResponseBody($response);

        // Add has_more field based on whether there's a next page
        $responseData['has_more'] = isset($responseData['paging']['next']);

        return $responseData;
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function loadMoreAds(string $nextUrl): array
    {
        $response = $this->client->get($nextUrl);
        $responseData = $this->extractJsonResponseBody($response);

        // Add has_more field based on whether there's a next page
        $responseData['has_more'] = isset($responseData['paging']['next']);

        return $responseData;
    }

    /**
     * @throws GuzzleException
     */
    public function subscribe(string $accessToken, string $pageId): void
    {
        $url = sprintf('%s/%s/%s/subscribed_apps', self::FACEBOOK_GRAPH_API, self::FACEBOOK_GRAPH_VERSION, $pageId);

        $this->client->post($url, [
            'json' => [
                'subscribed_fields' => ['leadgen'],
            ],
            'query' => [
                'access_token' => $accessToken,
            ],
        ]);
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function sampleFormResponse(string $accessToken, int $formId): array
    {
        $url = sprintf('%s/%s/%s/leads', self::FACEBOOK_GRAPH_API, self::FACEBOOK_GRAPH_VERSION, $formId);

        $response = $this->client->get($url, [
            'query' => [
                'access_token' => $accessToken,
                'fields' => 'field_data,created_time,ad_id,ad_name,adset_id,adset_name,source,campaign_id,campaign_name',
            ],
        ]);

        return $this->extractJsonResponseBody($response);
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function getAdInsights(string $accessToken, string $adId): array
    {
        $url = sprintf('%s/%s/%s', self::FACEBOOK_GRAPH_API, self::FACEBOOK_GRAPH_VERSION, $adId);

        $response = $this->client->get($url, [
            'query' => [
                'access_token' => $accessToken,
                'fields' => 'name,campaign{name},adset{name}',
            ],
        ]);

        return $this->extractJsonResponseBody($response);
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function fetchLeadInformation(string $leadgenId, string $accessToken): array
    {
        $url = sprintf('%s/%s/%s', self::FACEBOOK_GRAPH_API, self::FACEBOOK_GRAPH_VERSION, $leadgenId);

        try {
            $response = $this->client->get($url, [
                'query' => [
                    'access_token' => $accessToken,
                    'fields' => 'ad_name,adset_name,campaign_name,field_data',
                ],
            ]);
    
            return $this->extractJsonResponseBody($response);
        } catch (ClientException $e) {
            Log::info('Failed to fetch lead information', [
                'lead_gen_id' => $leadgenId,
                'exception' => $e->getMessage(),
            ]);

            $error = json_decode($e->getResponse()->getBody()->getContents(), true);
            if (isset($error['error']['code']) && $error['error']['code'] == 190) {
                throw new FacebookTokenInvalid($error['error']['message']);
            }
            
            throw $e;
        }
        
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function getLongLivedToken(string $shortLivedToken): array
    {
        $url = sprintf('%s/%s/oauth/access_token', self::FACEBOOK_GRAPH_API, self::FACEBOOK_GRAPH_VERSION);
        $response = $this->client->get($url, [
            'query' => [
                'grant_type'    => 'fb_exchange_token',
                'client_id'     => self::FACEBOOK_APP_ID,
                'client_secret' => self::FACEBOOK_APP_SECRET,
                'fb_exchange_token' => $shortLivedToken,
            ],
        ]);

        return $this->extractJsonResponseBody($response);
    }

    /**
     * @throws JsonException
     */
    private function extractJsonResponseBody(ResponseInterface $response): array
    {
        return (array) json_decode((string) $response->getBody(), true, 512, JSON_THROW_ON_ERROR);
    }
}